// === File: ACS712Example.ino ===
#include <Arduino.h>
#include "ACS712Meter.h"

// Instantiate meter on pin A5, output over Serial
ACS712Meter _currentSensorEL(A5);

void setup() {
  Serial.begin(115200);
  while (!Serial);
  Serial.println("--- ACS712Meter Example ---");
  float offsetV = _currentSensorEL.calibrate(500);  // calibrate with 500 samples
  
  Serial.print("Calibrated offset (V): ");
  Serial.println(offsetV, 4);

  Serial.println("Starting measurements...");
}

void loop() {
  float current = _currentSensorEL.readCurrent(50);  // 50-sample smoothing
  Serial.print("Current: ");
  Serial.print(current, 3);
  Serial.println(" A");
  delay(500);
}
