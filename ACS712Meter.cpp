#include "ACS712Meter.h"
// === File: ACS712Meter.cpp ===

ACS712Meter::ACS712Meter(
  uint8_t analogPin,
  float sensitivity,
  float vref,
  float adcRes)
  : pin(analogPin),
    sens(sensitivity),
    Vref(vref),
    ADCres(adcRes),
    offsetV(0.0f) {}

float ACS712Meter::calibrate(uint16_t samples) {
  long sum = 0;
  for (uint16_t i = 0; i < samples; ++i) {
    sum += analogRead(pin);
    delay(5);
  }
  float avg = sum / float(samples);
  offsetV = (avg * Vref) / ADCres;
  //serial.print("Calibrated offset (V): ");
  //serial.println(offsetV, 4);
  return offsetV;
}

float ACS712Meter::readCurrent(uint16_t samples) {
  long sum = 0;
  for (uint16_t i = 0; i < samples; ++i) {
    sum += analogRead(pin);
  }
  float avg = sum / float(samples);
  float voltage = (avg * Vref) / ADCres;
  return (voltage - offsetV) / sens;
}
