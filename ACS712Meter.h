#ifndef ACS712METER_H
#define ACS712METER_H

// === File: ACS712Meter.h ===

#include <Arduino.h>

class ACS712Meter {
public:
  /**
   * @param analogPin  Analog input pin (A0-A15)
   * @param sensitivity ACS712 sensitivity in V/A (default 0.185)
   * @param vref       ADC reference voltage (default 5.0V)
   * @param adcRes     ADC resolution (default 1023.0)
   */
  ACS712Meter(uint8_t analogPin,
              float sensitivity = 0.185,
              float vref = 5.0,
              float adcRes = 1023.0);

  /**
   * Calibrate zero-current offset (no load) using N samples
   * @param samples Number of readings to average
   */
  float calibrate(uint16_t samples);

  /**
   * Read current in amperes using N-sample smoothing
   * @param samples Number of readings to average
   * @return Measured current (A)
   */
  float readCurrent(uint16_t samples);

private:
  uint8_t pin;
  float sens;
  float Vref;
  float ADCres;
  float offsetV;
};

#endif // ACS712METER_H