#ifndef CONSTANTS_H
#define CONSTANTS_H

/**
 * Constants.h
 * Contains all system-wide constants, enums, and pin definitions
 * for the Elektrolizor project.
 */

#include <Arduino.h>

// Log levels for system messaging
enum LogLevel
{
  LOG_ERROR = 0,
  LOG_WARN,
  LOG_INFO,
  LOG_DEBUG
};
static const LogLevel currentLogLevel = LOG_INFO;

// Analog Sensor Indices
enum AnalogSensors
{
//LS1
//TT01
//PT1
//TT2
//PT2
//TT3
//PT3
//TT4

//VFC
//VBMS
//VEL
//CFC
//CBMS
//CEL
//14

  ADC_SENSOR_WATER_LEVEL = 0,//LS-01, ANALOG
  ADC_SENSOR_WATER_TEMP,//TT01
  ADC_SENSOR_WATER_PRESS,//PT01
  ADC_SENSOR_ELEC_TEMP,//TT-02
  ADC_SENSOR_H2_TANK_PRESS,//PT-02
  ADC_SENSOR_H2_TANK_TEMP,//TT-03
  ADC_SENSOR_O2_TANK_PRESS,//PT-03
  ADC_SENSOR_O2_TANK_TEMP,//TT-04
  ADC_FC_VOLTAGE,// VFC
  ADC_SENSOR_BMS_VOLTAGE,// VBMS
  ADC_SENSOR_ELEC_VOLTAGE,// VEL
  ADC_FC_CURRENT,// CFC
  ADC_SENSOR_BMS_CURRENT, // CBMS
  ADC_SENSOR_ELEC_CURRENT// CEL
};

// Digital Actuator Pins
enum DigitalActuator
{
  ACT_WATER_PUMP_RELAY = 22,//PM-02
  ACT_WATER_INLET_VALVE = 23,//SV-01
  ACT_ELEC_HEATER = 24,//EH-01
  ACT_H2_OUTPUT_VALVE = 25,//SV-03
  ACT_O2_OUTPUT_VALVE = 26,//SV-02
  ACT_H2_DRYER_DISCHARGE_VALVE = 27,//SV-04
  ACT_EL_O2_CHILLER_RELAY = 28,//CF-01
  ACT_ELEC_O2_DRYER_WATER_INLET_PUMP_RELAY = 29,//PM-01
  ACT_FC_H2_SUPPLY_VALVE = 30,//SV-05
  ACT_FC_O2_SUPPLY_VALVE = 31,//SV-06
  ACT_FC_O2_FAN = 32, // CF-06
  ACT_FC_H2_DISCHARGE_VALVE = 33,//SV-07
  ACT_FC_O2_CHILLER_RELAY = 34,//CF-02
  ACT_FC_O2_DRYER_PUMP = 35,//PM-03
  ACT_FC_LOAD_RELAY = 36,// R_FC_LOAD | INV FC - to 5v usb & inverter
  ACT_BMS_CHARGE_RELAY = 37, // R_BMS_CHARGE | BMS GİRİŞ
  ACT_BMS_DISCHARGE_RELAY = 38, //R_BMS_DISCHARGE | BMS ÇIKIŞ
  ACT_INVERTER_RELAY = 39, //R_INVERTER |
  ACT_EMERGENCY_VENT = 40,     // SAFETY
  ACT_MAIN_INPUT_RELAY = 41,   //R_MAIN_INPUT & EL PSU // SAFETY
  ACT_MAIN_BMS_OUT_RELAY = 42, //R_BMS_OUT // | INV BAT - SAFETY
  ACTUATOR_COUNT
};

// Digital Input Pins
enum DigitalInput
{
  PIN_ELEC_O2_DRYER_LEVEL = 47, // LS-02
  PIN_ELEC_H2_DRYER_LEVEL = 48, //LS-03
  PIN_FC_O2_DRYER_LEVEL = 49, //LS-04
  PIN_SENSOR_FIRE_DETECT = 50, //FS-01, SAFETY
  PIN_SENSOR_H2_AMBIENT = 51,  //HS-01, SAFETY
  PIN_EMERGENCY_STOP = 52,     //ES-01 SAFETY
  DIGITAL_INPUT_COUNT
};

// Sensor Calibration Indices
enum SensorCalIndex
{
  CAL_WATER_LEVEL = 0,
  CAL_WATER_TEMP,
  CAL_WATER_PRESS,
  CAL_ELEC_TEMP,
  CAL_H2_TANK_PRESS,
  CAL_H2_TANK_TEMP,
  CAL_O2_TANK_PRESS,
  CAL_O2_TANK_TEMP,
  CAL_FC_VOLTAGE,
  CAL_BMS_VOLTAGE,
  CAL_ELEC_VOLTAGE,
  CAL_FC_CURRENT,
  CAL_BMS_CURRENT,
  CAL_ELEC_CURRENT,
  CAL_SENSOR_COUNT
};

// Telemetry Command Types
enum TelemetryCommandType
{
  CMD_MODE_CHANGE_REQUEST = 0x01,
  CMD_CALIBRATION_GET = 0x02,
  CMD_CALIBRATION_UPDATE = 0x03,
  CMD_TELEMETRY_DATA = 0x04,
  CMD_ACKNOWLEDGMENT = 0x05,
  CMD_LOG_MESSAGE = 0x06,
  CMD_TELEMETRY_REQUEST = 0x07,
  CMD_SIMULATION_UPDATE = 0x08
};

// ADS1115 Pins
#define ADS1_ALERT_PIN 43
#define ADS2_ALERT_PIN 44
#define ADS3_ALERT_PIN 45
#define ADS4_ALERT_PIN 46

// ELRegulator Pins
#define EL_REGULATOR_PWM_PIN 9        // PWM → PSU PWM input
#define EL_REGULATOR_VOLT_MON_PIN A2  // 0–5 V monitor (maps to 0–15 V)
#define EL_REGULATOR_CURR_MON_PIN A3  // 0–5 V monitor (maps to 0–55 A)

// ACS712 Current Sensor Pins
#define CURRENT_SENSOR_PIN_EL A5      // ACS712 current sensor for electrolyzer
#define CURRENT_SENSOR_PIN_FC A6      // ACS712 current sensor for fuel cell

// Voltage Sensor Pins
#define VOLTAGE_SENSOR_PIN_EL A7      // Voltage divider for electrolyzer voltage
#define VOLTAGE_SENSOR_PIN_FC A8      // Voltage divider for fuel cell voltage

// ADS1115 Settings
#define ADS_GAIN GAIN_TWOTHIRDS
#define ADS_SPS RATE_ADS1115_250SPS
#define ADS_FULL_SCALE 6.144

// PZEM Settings
#define NUM_PZEMS 3

// #if !defined(PZEM_RX_PIN)
#define PZEM_RX_PIN 16
// #endif

// #if !defined(PZEM_TX_PIN)
#define PZEM_TX_PIN 17
// #endif

#if !defined(PZEM_SERIAL)
#define PZEM_SERIAL Serial2
#endif

// SERIAL SETTINGS
//  Aliases for clarity
#if !defined(DEBUG_SERIAL)
#define DEBUG_SERIAL Serial1 // USB serial for debug/logging
#endif

#if !defined(TELEMETRY_SERIAL)
#define TELEMETRY_SERIAL Serial // Hardware serial 1 for telemetry
#endif

// Simulation Mode
#define SIM_MODE_INITIAL true
#define NUM_ANALOG_SENSORS 14
#define SIM_UPDATE_PAYLOAD_LENGTH 205

// Predictive Buffer Size
#define PREDICTIVE_SAMPLES 30

/**
 * System Constants - Operational parameters for the system
 * Includes timing, safety thresholds, and operational limits
 */
namespace Constants
{
  // Timing constants (milliseconds)
  const unsigned long CONTROL_LOOP_FREQ_MS = 100;   // Main control loop frequency
  const unsigned long TELEMETRY_INTERVAL_MS = 1000; // Telemetry update interval
  const unsigned long MODE_CHANGE_DELAY_MS = 5000;  // Delay for mode transitions
  const unsigned long MAIN_LOOP_WATCHDOG_MS = 2000; // Watchdog timeout

  // Mode transition timeout multiplier
  const unsigned int MODE_TRANSITION_TIMEOUT_MULTIPLIER = 2; // Multiplier for calculating transition timeout

  // Controller timing constants
  const unsigned long ELECTROLYZER_MIN_SWITCH_INTERVAL = 5000; // 5 seconds minimum between electrolyzer component switches
  const unsigned long DRYER_MIN_TOGGLE_INTERVAL = 10000;       // 10 seconds minimum between dryer toggles

  // SystemController mode transition timing constants
  const unsigned long ELECTROLYZER_ACTIVATION_DELAY_MS = 500;  // Delay before activating heater in electrolyzer mode
  const unsigned long FUEL_CELL_ACTIVATION_DELAY_MS = 1000;    // Delay before activating load relay in fuel cell mode
  const unsigned long BATTERY_INVERTER_ACTIVATION_DELAY_MS = 2000; // Delay before activating inverter in battery mode
  const unsigned long SYSTEM_TEST_PIN_TOGGLE_INTERVAL_MS = 200;    // Interval for toggling pins during system test

  // SafetyController timing constants
  const unsigned long H2_LEAK_CLEAR_TIMEOUT_MS = 10000;       // Time to wait before clearing H2 leak fault
  const unsigned long COMM_WATCHDOG_TIMEOUT_MS = 5000;        // Communication timeout threshold

  // Main loop timing constants
  const unsigned long WATCHDOG_RESET_INTERVAL_MS = 1000;      // Watchdog timer reset interval

  // Safety thresholds
  const float H2_LEAK_THRESHOLD_PERCENT = 4.0f; // H2 leak detection threshold
  const float MAX_OPERATING_TEMP_C = 80.0f;     // Maximum safe operating temperature
  const float MAX_TANK_PRESSURE_BAR = 5.0f;     // Maximum tank pressure

  // Electrolyzer operational constants
  const float WATER_PRESSURE_START = 3.5f;      // Water pressure to start pump (bar)
  const float WATER_PRESSURE_STOP = 4.0f;       // Water pressure to stop pump (bar)
  const float WATER_LEVEL_MIN = 2.0f;           // Minimum water level (%)
  const float WATER_TEMP_LOW = 20.0f;           // Low water temperature threshold (°C)
  const float WATER_TEMP_TARGET = 80.0f;        // Target water temperature (°C)
  const float WATER_TEMP_HIGH = 85.0f;          // High water temperature threshold (°C)
  const float ELEC_TEMP_HIGH = 85.0f;           // High electrolyzer temperature threshold (°C)
  const float PRODUCTION_START_TEMP = 65.0f;    // Temperature to start production (°C)
  const float PRODUCTION_STOP_TEMP = 63.0f;     // Temperature to stop production (°C)

  // Temperature control hysteresis constants
  const float TEMP_HYSTERESIS_OFFSET = 5.0f;    // Temperature offset for hysteresis control (°C)

  // Fuel cell operational constants
  const float MIN_OPERATING_VOLTAGE = 1.0f;     // Minimum fuel cell operating voltage (V)
  const float MAX_OPERATING_VOLTAGE = 16.0f;    // Maximum fuel cell operating voltage (V)
  const float MIN_OUTPUT_POWER = 1.0f;          // Minimum fuel cell output power (W)
  const float MAX_OUTPUT_POWER = 110.0f;        // Maximum fuel cell output power (W)
  const float OUTPUT_POWER_SETPOINT = 100.0f;   // Fuel cell power setpoint (W)

  // ELRegulator constants
  const float EL_REGULATOR_SUPPLY_MAX_V = 15.0f; // full-scale voltage
  const float EL_REGULATOR_SUPPLY_MAX_I = 55.0f; // full-scale current
  const float EL_REGULATOR_ADC_REF = 5.0f;       // Arduino analog reference (DEFAULT = Vcc)
  const int EL_REGULATOR_ADC_MAX = 1023;         // 10-bit max

  // Battery parameters
  const float BATT_MAX_VOLTAGE_V = 29.2f;      // Maximum battery voltage
  const float BATT_MIN_VOLTAGE_V = 20.0f;      // Minimum battery voltage
  const float BATT_MAX_TEMP_C = 60.0f;         // Maximum battery temperature
  const float LOW_BATTERY_THRESHOLD = 21.0f;   // Low battery warning threshold
  const float CHARGE_OFF_THRESHOLD = 28.0f;    // Stop charging threshold
  const float DISCHARGE_ON_THRESHOLD = 28.0f;  // Start discharge threshold
  const float DISCHARGE_OFF_THRESHOLD = 20.0f; // Stop discharge threshold
}

// Calibration Constants
static const uint32_t CALIBRATION_MAGIC_VALUE = 0xB1ADBEEF;
static const uint32_t CALIBRATION_VERSION = 0x10000001;

#endif // CONSTANTS_H
