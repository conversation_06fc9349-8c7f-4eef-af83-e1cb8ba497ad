#ifndef CONTROLLERS_H
#define CONTROLLERS_H

/**
 * Controllers.h
 * Central header file for all global controller instances
 * Provides a single include point for accessing all system controllers
 * and the ELRegulator instance
 */

// Forward declarations to avoid circular dependencies
class SystemController;
class SafetyController;
class ElectrolyzerController;
class FuelCellController;
class DryerController;
class TelemetryController;
class ELRegulator;

// Global controller instances
extern SystemController system_controller;
extern SafetyController safety_controller;
extern ElectrolyzerController electrolyzer_controller;
extern FuelCellController fuel_cell_controller;
extern DryerController dryer_controller;
extern TelemetryController telemetry_controller;
extern ELRegulator elRegulator;

#endif // CONTROLLERS_H
