#ifndef DRYER_CONTROLLER_H
#define DRYER_CONTROLLER_H

/**
 * DryerController.h
 * Controls the gas drying systems for both electrolyzer and fuel cell
 * Manages water removal from H2 and O2 gas streams
 */

#include <Arduino.h>
#include "Constants.h"
#include "Sensors.h"
#include "Utilities.h"

/**
 * DryerController class
 * Monitors dryer levels and controls discharge valves and pumps
 * to maintain proper gas dryness
 */
class DryerController {
private:
  uint8_t sensorElO2;
  uint8_t sensorElH2;
  uint8_t sensorFcO2;
  bool elO2DryerActive;
  bool elH2DryerActive;
  bool fcO2DryerActive;
  unsigned long lastElO2DryerToggle;
  unsigned long lastElH2DryerToggle;
  unsigned long lastFcO2DryerToggle;

public:
  DryerController();

  /**
   * Initialize dryer controller
   * Sets up input pins and initial states
   */
  void begin();

  /**
   * Main update method - monitors dryer levels and controls discharge
   * Called periodically from the main loop
   */
  void update();

  /**
   * Check if electrolyzer O2 dryer is active
   * @return True if dryer discharge is active
   */
  bool isElO2DryerActive() const { return elO2DryerActive; }

  /**
   * Check if electrolyzer H2 dryer is active
   * @return True if dryer discharge is active
   */
  bool isElH2DryerActive() const { return elH2DryerActive; }

  /**
   * Check if fuel cell O2 dryer is active
   * @return True if dryer discharge is active
   */
  bool isFcO2DryerActive() const { return fcO2DryerActive; }
};

extern DryerController dryer_controller;

#endif // DRYER_CONTROLLER_H
