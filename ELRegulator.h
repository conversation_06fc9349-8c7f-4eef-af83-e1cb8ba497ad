#ifndef ELREGULATOR_H
#define ELREGULATOR_H

/**
 * ELRegulator.h
 * Controls the electrolyzer power supply regulator
 * Provides PWM control for voltage output and monitoring of supply voltage/current
 */

#include <Arduino.h>
#include "Constants.h"

/**
 * ELRegulator class
 * Manages the electrolyzer power supply regulation through PWM control
 * and provides monitoring of supply voltage and current
 */
class ELRegulator {
private:
  float desiredVoltage;  // Current desired output voltage

public:
  /**
   * Constructor
   * Initializes the ELRegulator with default settings
   */
  ELRegulator();

  /**
   * Initialize the regulator
   * Sets up PWM pin and initializes to 0V output
   */
  void begin();

  /**
   * Set the desired output voltage
   * @param voltage Desired voltage (0.0 to 15.0V)
   */
  void setOutputVoltage(float voltage);

  /**
   * Read raw pin voltage (0-5V range)
   * @param pin Analog pin to read
   * @return Pin voltage in volts (0.0 to 5.0V)
   */
  float readPinVoltage(uint8_t pin);

  /**
   * Read the supply voltage
   * Scales the voltage monitor reading from 0-5V to 0-15V
   * @return Supply voltage in volts (0.0 to 15.0V)
   */
  float readSupplyVoltage();

  /**
   * Read the supply current
   * Scales the current monitor reading from 0-5V to 0-55A
   * @return Supply current in amperes (0.0 to 55.0A)
   */
  float readSupplyCurrent();

  /**
   * Get the current desired voltage setting
   * @return Current desired voltage in volts
   */
  float getDesiredVoltage() const { return desiredVoltage; }
};

#endif // ELREGULATOR_H
