#include "ElectrolyzerController.h"
#include "SafetyController.h"
#include "ELRegulator.h"

// Initialize the global electrolyzer controller
ElectrolyzerController electrolyzer_controller;

ElectrolyzerController::ElectrolyzerController()
  : lastPumpSwitchTime(0), lastHeaterSwitchTime(0),
    lastChillerSwitchTime(0), lastProductionSwitchTime(0), emergencyActive(false) {
  memset(&elecState, 0, sizeof(elecState));
}

void ElectrolyzerController::update() {
  // Check if SafetyController has already detected system-wide faults
  if (safety_controller.getFaultStatus() != 0) {
    // SafetyController has active faults - defer to system-wide emergency handling
    if (!emergencyActive) {
      logMessage(LOG_INFO, F("Electrolyzer: Deferring to SafetyController emergency handling"));
      emergencyActive = true;
    }
    return;
  }

  if (emergencyActive) {
    // In emergency mode, skip normal operations.
    return;
  }

  updateADSReadings();
  elecState.elecTemp   = getFilteredCalibratedValueADS(ADC_SENSOR_ELEC_TEMP, CAL_ELEC_TEMP);
  elecState.waterTemp  = getFilteredCalibratedValueADS(ADC_SENSOR_WATER_TEMP, CAL_WATER_TEMP);
  elecState.waterPressure = getFilteredCalibratedValueADS(ADC_SENSOR_WATER_PRESS, CAL_WATER_PRESS);
  elecState.waterLevel = getFilteredCalibratedValueADS(ADC_SENSOR_WATER_LEVEL, CAL_WATER_LEVEL);
  elecState.elecVoltage= getVolt_EL();
  elecState.elecCurrent= getACSCurrent_EL();

  unsigned long now = millis();

  // Check water level - report to SafetyController for centralized handling
  if (elecState.waterLevel < Constants::WATER_LEVEL_MIN) {
    logMessage(LOG_ERROR, F("Electrolyzer: Insufficient water level!"));
    safety_controller.reportSubsystemFault(SafetyController::FAULT_LOW_WATER_LEVEL);
    return;
  }
 
  // Water pump (and inlet valve) control with debounce
  if (!elecState.isWaterPumpOn && (elecState.waterPressure < Constants::WATER_PRESSURE_START) &&
      (now - lastPumpSwitchTime >= Constants::ELECTROLYZER_MIN_SWITCH_INTERVAL)) {
    elecState.isWaterPumpOn = true;
    SetDigitalOutputVal(ACT_WATER_PUMP_RELAY, LOW);
    SetDigitalOutputVal(ACT_WATER_INLET_VALVE, LOW);
    lastPumpSwitchTime = now;
    logMessage(LOG_INFO, F("Electrolyzer: Water pump activated."));
  }
  else if (elecState.isWaterPumpOn && (elecState.waterPressure > Constants::WATER_PRESSURE_STOP) &&
           (now - lastPumpSwitchTime >= Constants::ELECTROLYZER_MIN_SWITCH_INTERVAL)) {
    elecState.isWaterPumpOn = false;
    SetDigitalOutputVal(ACT_WATER_PUMP_RELAY, HIGH);
    SetDigitalOutputVal(ACT_WATER_INLET_VALVE, HIGH);
    lastPumpSwitchTime = now;
    logMessage(LOG_INFO, F("Electrolyzer: Water pump deactivated."));
  }
 
  // Heater control with debounce
  if (!elecState.isHeaterOn && (elecState.waterTemp < Constants::WATER_TEMP_LOW) &&
      (now - lastHeaterSwitchTime >= Constants::ELECTROLYZER_MIN_SWITCH_INTERVAL)) {
    elecState.isHeaterOn = true;
    SetDigitalOutputVal(ACT_ELEC_HEATER, LOW);
    lastHeaterSwitchTime = now;
    logMessage(LOG_INFO, F("Electrolyzer: Heater activated (water cold)."));
  }
  else if (elecState.isHeaterOn && (elecState.waterTemp >= Constants::WATER_TEMP_TARGET) &&
           (now - lastHeaterSwitchTime >= Constants::ELECTROLYZER_MIN_SWITCH_INTERVAL)) {
    elecState.isHeaterOn = false;
    SetDigitalOutputVal(ACT_ELEC_HEATER, HIGH);
    lastHeaterSwitchTime = now;
    logMessage(LOG_INFO, F("Electrolyzer: Heater deactivated (water warmed)."));
  }
 
  // Chiller control with debounce
  if (!elecState.isChillerOn && (elecState.elecTemp > Constants::ELEC_TEMP_HIGH) &&
      (now - lastChillerSwitchTime >= Constants::ELECTROLYZER_MIN_SWITCH_INTERVAL)) {
    elecState.isChillerOn = true;
    SetDigitalOutputVal(ACT_EL_O2_CHILLER_RELAY, LOW);
    lastChillerSwitchTime = now;
    logMessage(LOG_INFO, F("Electrolyzer: Chiller activated (elecTemp high)."));
  }
  else if (elecState.isChillerOn && (elecState.elecTemp < (Constants::ELEC_TEMP_HIGH - Constants::TEMP_HYSTERESIS_OFFSET)) &&
           (now - lastChillerSwitchTime >= Constants::ELECTROLYZER_MIN_SWITCH_INTERVAL)) {
    elecState.isChillerOn = false;
    SetDigitalOutputVal(ACT_EL_O2_CHILLER_RELAY, HIGH);
    lastChillerSwitchTime = now;
    logMessage(LOG_INFO, F("Electrolyzer: Chiller deactivated (elecTemp normalized)."));
  }
 
  // Production control with debounce
  bool shouldProduce = (elecState.elecTemp >= Constants::PRODUCTION_START_TEMP) &&
                       (elecState.waterTemp >= Constants::WATER_TEMP_LOW + Constants::TEMP_HYSTERESIS_OFFSET) &&
                       (elecState.waterLevel >= Constants::WATER_LEVEL_MIN);
  if (!elecState.isProducing && shouldProduce &&
      (now - lastProductionSwitchTime >= Constants::ELECTROLYZER_MIN_SWITCH_INTERVAL)) {
    elecState.isProducing = true;
    SetDigitalOutputVal(ACT_H2_OUTPUT_VALVE, LOW);
    SetDigitalOutputVal(ACT_O2_OUTPUT_VALVE, LOW);
    elRegulator.setOutputVoltage(10.0f);  // Set regulator to 10V when production starts
    lastProductionSwitchTime = now;
    logMessage(LOG_INFO, F("Electrolyzer: Production started."));
  }
  else if (elecState.isProducing && ((!shouldProduce) || (elecState.elecTemp < Constants::PRODUCTION_STOP_TEMP)) &&
           (now - lastProductionSwitchTime >= Constants::ELECTROLYZER_MIN_SWITCH_INTERVAL)) {
    elecState.isProducing = false;
    SetDigitalOutputVal(ACT_H2_OUTPUT_VALVE, HIGH);
    SetDigitalOutputVal(ACT_O2_OUTPUT_VALVE, HIGH);
    elRegulator.setOutputVoltage(0.0f);  // Set regulator to 0V when production stops
    lastProductionSwitchTime = now;
    logMessage(LOG_INFO, F("Electrolyzer: Production stopped."));
  }
 
  // Emergency temperature check - report to SafetyController for centralized handling
  if (elecState.elecTemp > (ELEC_TEMP_HIGH + 10.0f) || elecState.waterTemp > WATER_TEMP_HIGH) {
    logMessage(LOG_ERROR, F("Electrolyzer: Temperature emergency!"));
    safety_controller.reportSubsystemFault(SafetyController::FAULT_OVERTEMP);
  }
}

void ElectrolyzerController::emergency_stop() {
  logMessage(LOG_ERROR, F("Electrolyzer: EMERGENCY STOP triggered."));
  SetDigitalOutputVal(ACT_ELEC_HEATER, HIGH);
  SetDigitalOutputVal(ACT_H2_OUTPUT_VALVE, HIGH);
  SetDigitalOutputVal(ACT_O2_OUTPUT_VALVE, HIGH);
  SetDigitalOutputVal(ACT_WATER_PUMP_RELAY, HIGH);
  SetDigitalOutputVal(ACT_WATER_INLET_VALVE, HIGH);
  SetDigitalOutputVal(ACT_EL_O2_CHILLER_RELAY, HIGH);
  elRegulator.setOutputVoltage(0.0f);  // Set regulator to 0V during emergency stop
  elecState.isHeaterOn     = false;
  elecState.isProducing    = false;
  elecState.isWaterPumpOn  = false;
  elecState.isChillerOn    = false;
  emergencyActive    = true;
}

void ElectrolyzerController::resetEmergency() {
  emergencyActive = false;
  logMessage(LOG_INFO, F("Electrolyzer: Emergency reset."));
}

void ElectrolyzerController::stopProduction() {
  logMessage(LOG_INFO, F("Electrolyzer: Stopping production (mode deactivation)."));
  elecState.isProducing = false;
  elecState.isHeaterOn = false;
  elecState.isWaterPumpOn = false;
  elecState.isChillerOn = false;
}
