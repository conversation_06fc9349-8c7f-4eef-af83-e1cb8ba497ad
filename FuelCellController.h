#ifndef FUEL_CELL_CONTROLLER_H
#define FUEL_CELL_CONTROLLER_H

/**
 * FuelCellController.h
 * Controls the fuel cell subsystem for power generation
 * Manages gas flow, power output, and safety monitoring
 */

#include <Arduino.h>
#include <PID_v1.h>
#include "Constants.h"
#include "Sensors.h"
#include "Utilities.h"

/**
 * FuelCellController class
 * Manages fuel cell operation including gas supply,
 * power output control, and safety monitoring
 */
class FuelCellController {
public:
  /**
   * State structure containing fuel cell operational parameters
   */
  struct State {
    float fcCurrent;    // Fuel cell current (A)
    float fcVoltage;    // Fuel cell voltage (V)
    float outputPower;  // Output power (W)
    bool isActive;      // Active state
  };

  static constexpr float MIN_OPERATING_VOLTAGE = 1.0f;
  static constexpr float MAX_OPERATING_VOLTAGE = 16.0f;
  static constexpr float MIN_OUTPUT_POWER = 1.0f;
  static constexpr float MAX_OUTPUT_POWER = 110.0f;
  static constexpr float OUTPUT_POWER_SETPOINT = 100.0f;

private:
  State fcState;
  double pidSetpoint;
  double pidInput;
  double pidOutput;
  PID pid;

public:
  FuelCellController();

  /**
   * Main update method - monitors and controls fuel cell operation
   * Checks voltage, current, and power output for safe operation
   */
  void update();

  /**
   * Activate or deactivate the fuel cell
   * @param enable True to activate, false to deactivate
   */
  void activate(bool enable);

  /**
   * Emergency shutdown of the fuel cell
   * Called when unsafe conditions are detected
   */
  void shutdown();

  /**
   * Deactivate fuel cell and reset state
   * Called when deactivating fuel cell mode
   */
  void deactivate();

  /**
   * Get the current fuel cell state
   * @return Reference to the current state structure
   */
  const State& getState() const { return fcState; }
};

extern FuelCellController fuel_cell_controller;

#endif // FUEL_CELL_CONTROLLER_H
