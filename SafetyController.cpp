#include "SafetyController.h"
#include "SystemController.h"
#include "ElectrolyzerController.h"

// Initialize the global safety controller
SafetyController safety_controller;

SafetyController::SafetyController() : activeFaults(0), lastCheckMs(0), lastCommMs(millis()) {}

void SafetyController::update() {
  unsigned long now = millis();
  if (now - lastCheckMs < Constants::CONTROL_LOOP_FREQ_MS) return;
  lastCheckMs = now;

  uint8_t newFaults = 0;
  
  // H2 leak detection removed - sensor not available
  // // Check for H2 leak
  // TODO: REVISE FOR DIGITAL INPUT PIN_SENSOR_H2_AMBIENT CONNECTED TO RELAY
  // float h2Ambient = getFilteredCalibratedValueADS(ADC_SENSOR_H2_AMBIENT, CAL_SENSOR_H2_AMBIENT);
  // if (h2Ambient > Constants::H2_LEAK_THRESHOLD_PERCENT) {
  //   newFaults |= FAULT_H2_LEAK;
  //   logMessage(LOG_ERROR, F("Safety: H₂ leak detected. Ambient="), String(h2Ambient, 2));
  // }

  // Check for overpressure
  float h2TankPress = getFilteredCalibratedValueADS(ADC_SENSOR_H2_TANK_PRESS, CAL_H2_TANK_PRESS);
  float o2TankPress = getFilteredCalibratedValueADS(ADC_SENSOR_O2_TANK_PRESS, CAL_O2_TANK_PRESS);
  if ((h2TankPress > Constants::MAX_TANK_PRESSURE_BAR) ||
      (o2TankPress > Constants::MAX_TANK_PRESSURE_BAR)) {
    newFaults |= FAULT_OVERPRESSURE;
    logMessage(LOG_ERROR, F("Safety: Tank overpressure. H2="), String(h2TankPress, 2));
    logMessage(LOG_ERROR, F(" O2="), String(o2TankPress, 2));
  }

  // Check for overtemperature
  float elecTemp = getFilteredCalibratedValueADS(ADC_SENSOR_ELEC_TEMP, CAL_ELEC_TEMP);
  float h2Temp = getFilteredCalibratedValueADS(ADC_SENSOR_H2_TANK_TEMP, CAL_H2_TANK_TEMP);
  float o2Temp = getFilteredCalibratedValueADS(ADC_SENSOR_O2_TANK_TEMP, CAL_O2_TANK_TEMP);
  if ((elecTemp > Constants::MAX_OPERATING_TEMP_C) ||
      (h2Temp > Constants::MAX_OPERATING_TEMP_C) ||
      (o2Temp > Constants::MAX_OPERATING_TEMP_C)) {
    newFaults |= FAULT_OVERTEMP;
    logMessage(LOG_ERROR, F("Safety: Overtemp. ElecTemp="), String(elecTemp, 2));
  }

  // // Check for fire
  // if (GetDigitalInputVal(PIN_SENSOR_FIRE_DETECT) == HIGH) {
  //   newFaults |= FAULT_OVERTEMP;
  //   logMessage(LOG_ERROR, F("Safety: Fire. ElecTemp="), String(elecTemp, 2));
  // }

  // Check water level
  float waterLevel = getFilteredCalibratedValueADS(ADC_SENSOR_WATER_LEVEL, CAL_WATER_LEVEL);
  if (waterLevel < 1.0f) {
    newFaults |= FAULT_LOW_WATER_LEVEL;
    logMessage(LOG_ERROR, F("Safety: Water level too low. Level="), String(waterLevel, 2));
  }

  // // Check for communication loss
  // if ((now - lastCommMs) > Constants::COMM_WATCHDOG_TIMEOUT_MS) {
  //   newFaults |= FAULT_COMMS_LOSS;
  //   logMessage(LOG_ERROR, F("Safety: Communication loss detected."));
  // }

  // // Predictive checks
  // if (waterTempBuffer.count == PREDICTIVE_SAMPLES) {
  //   float slope = calculate_slope(waterTempBuffer.history, waterTempBuffer.count, 0.1f);
  //   if (fabs(slope) > 0.2f) {
  //     newFaults |= FAULT_PREDICTIVE;
  //     logMessage(LOG_ERROR, F("Safety: Rapid water temp change detected."));
  //   }
  // }

  // if (elecTempBuffer.count == PREDICTIVE_SAMPLES) {
  //   float slope = calculate_slope(elecTempBuffer.history, elecTempBuffer.count, 0.1f);
  //   if (fabs(slope) > 0.3f) {
  //     newFaults |= FAULT_PREDICTIVE;
  //     logMessage(LOG_ERROR, F("Safety: Rapid elec temp change detected."));
  //   }
  // }

  // if (bmsTempBuffer.count == PREDICTIVE_SAMPLES) {
  //   float slope = calculate_slope(bmsTempBuffer.history, bmsTempBuffer.count, 0.1f);
  //   if (fabs(slope) > 0.2f) {
  //     newFaults |= FAULT_PREDICTIVE;
  //     logMessage(LOG_ERROR, F("Safety: Rapid BMS temp change detected."));
  //   }
  // }

  // Set active faults
  activeFaults |= newFaults;

  // H2 leak fault clearing logic removed - sensor not available
  
  // H2 leak fault clearing logic
  //TODO: REVISE FOR DIGITAL INPUT PIN_SENSOR_H2_AMBIENT CONNECTED TO RELAY
  // static unsigned long h2LeakClearStart = 0;
  // if (activeFaults & FAULT_H2_LEAK) {
  //   float h2Ambient = getFilteredCalibratedValueADS(ADC_SENSOR_H2_AMBIENT, CAL_SENSOR_H2_AMBIENT);
  //   if (h2Ambient < (Constants::H2_LEAK_THRESHOLD_PERCENT - 0.3f)) {
  //     if (h2LeakClearStart == 0)
  //       h2LeakClearStart = millis();
  //     else if (millis() - h2LeakClearStart > Constants::H2_LEAK_CLEAR_TIMEOUT_MS) {
  //       activeFaults &= ~FAULT_H2_LEAK;
  //       h2LeakClearStart = 0;
  //       logMessage(LOG_INFO, F("Safety: H₂ leak fault cleared."));
  //     }
  //   } else {
  //     h2LeakClearStart = 0;
  //   }
  // }

  // Trigger emergency shutdown if any faults are active
  if (activeFaults != 0) {
    emergency_shutdown();
  }
}

void SafetyController::latchSelfTestFail() {
  activeFaults |= FAULT_SELFTEST_FAIL;
}

void SafetyController::resetCommWatchdog() {
  lastCommMs = millis();
}

uint8_t SafetyController::getFaultStatus() const {
  return activeFaults;
}

void SafetyController::emergency_shutdown() {
  logMessage(LOG_ERROR, F("Safety: EMERGENCY shutdown initiated."));

  // Set system to emergency mode (force_mode doesn't trigger transitions, so no circular calls)
  if (system_controller.get_mode() != SystemController::MODE_EMERGENCY) {
    system_controller.force_mode(SystemController::MODE_EMERGENCY);
  }

  // Perform immediate emergency shutdown actions
  // Turn off all actuators except emergency vent
  for (uint8_t pin = 22; pin < ACTUATOR_COUNT; pin++) {
    if(pin != 43){//ACT_EMERGENCY_VENT
      SetDigitalOutputVal(pin, HIGH);
    }
  }

  // Set EL Regulator to 0V for safety
  elRegulator.setOutputVoltage(0.0f);

  // Activate emergency vent
  SetDigitalOutputVal(ACT_EMERGENCY_VENT, LOW);// Turn ON emergency vent

  // Trigger emergency stop in subsystem controllers
  electrolyzer_controller.emergency_stop();
}

bool SafetyController::isEmergencyMode() const {
  return system_controller.get_mode() == SystemController::MODE_EMERGENCY;
}

void SafetyController::reportSubsystemFault(FaultCode faultCode) {
  logMessage(LOG_ERROR, F("Safety: Subsystem fault reported: "), String(faultCode, HEX));
  activeFaults |= faultCode;
  // Emergency shutdown will be triggered on next update() call
}
