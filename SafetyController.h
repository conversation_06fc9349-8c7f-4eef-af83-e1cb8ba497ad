#ifndef SAFETY_CONTROLLER_H
#define SAFETY_CONTROLLER_H

/**
 * SafetyController.h
 * Monitors system safety parameters and triggers emergency shutdown when needed
 * Detects conditions like H2 leaks, overpressure, overtemperature, etc.
 */

#include <Arduino.h>
#include "Constants.h"
#include "Sensors.h"
#include "Utilities.h"

/**
 * SafetyController class
 * Responsible for monitoring safety-critical parameters and triggering
 * appropriate responses to unsafe conditions
 */
class SafetyController {
public:
  enum FaultCode : uint8_t {
    FAULT_H2_LEAK = 0x01,
    FAULT_OVERPRESSURE = 0x02,
    FAULT_OVERTEMP = 0x04,
    FAULT_COMMS_LOSS = 0x08,
    FAULT_PREDICTIVE = 0x10,
    FAULT_SELFTEST_FAIL = 0x20,
    FAULT_LOW_WATER_LEVEL = 0x40,
    FAULT_FC_VOLTAGE = 0x80
  };

private:
  uint8_t activeFaults;
  unsigned long lastCheckMs;
  unsigned long lastCommMs;

public:
  SafetyController();

  /**
   * Main update method - checks all safety parameters
   * Called periodically from the main loop
   */
  void update();

  /**
   * Sets the self-test failure flag
   * Used when system self-tests detect problems
   */
  void latchSelfTestFail();

  /**
   * Resets the communication watchdog timer
   * Called when valid communication is received
   */
  void resetCommWatchdog();

  /**
   * Returns the current fault status bitmap
   * @return Bitmap of active fault codes
   */
  uint8_t getFaultStatus() const;

  /**
   * Initiates emergency shutdown of the system
   * Forces system to emergency mode and turns off all actuators
   */
  void emergency_shutdown();

  /**
   * Checks if the system is currently in emergency mode
   * @return True if system is in emergency mode
   */
  bool isEmergencyMode() const;

  /**
   * Reports a subsystem fault to the safety controller
   * Allows subsystem controllers to report faults for centralized handling
   * @param faultCode The fault code to report
   */
  void reportSubsystemFault(FaultCode faultCode);
};

extern SafetyController safety_controller;

#endif // SAFETY_CONTROLLER_H