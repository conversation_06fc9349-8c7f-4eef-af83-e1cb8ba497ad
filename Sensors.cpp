#include <Arduino.h>
#include "Constants.h"
#include "Sensors.h"
#include <EEPROM.h>
#include <string.h>
#include <math.h>
#include "VoltageSensor.h"
#include "ACS712Meter.h"
#include "ELRegulator.h"
#include "Simulation.h"
#include <HardwareSerial.h>

// Default calibration data
const CalibrationData default_calibration = {
    CALIBRATION_VERSION,
    {
        {0.0f, 100.0f}, {0.0f, 25.0f}, {0.0f, 0.0f}, {0.0f, 25.0f}, {0.0f, 0.0f},
        {0.0f, 25.0f}, {0.0f, 0.0f}, {0.0f, 25.0f}, {0.0f, 0.0f}, {0.0f, 0.0f},
        {0.0f, 0.0f}, {0.0f, 0.0f}, {0.0f, 30.0f}, {0.0f, 10.0f}
    },
    CALIBRATION_MAGIC_VALUE};

// Global variables
CalibrationData calibration_data;
MovingAverage sensorFilters[CAL_SENSOR_COUNT];
PredictiveBuffer waterTempBuffer, elecTempBuffer, bmsTempBuffer;
Adafruit_ADS1115 ads1; // I2C address 0x48
Adafruit_ADS1115 ads2; // I2C address 0x49
Adafruit_ADS1115 ads3; // I2C address 0x4A
Adafruit_ADS1115 ads4; // I2C address 0x4B
ADS1115Reader adsReaders[4];
PZEM004Tv30 pzems[NUM_PZEMS];
ACS712Meter _currentSensorEL(CURRENT_SENSOR_PIN_EL);
ACS712Meter _currentSensorFC(CURRENT_SENSOR_PIN_FC);

VoltageSensor _voltageSensorEL(
    VOLTAGE_SENSOR_PIN_EL,
    VOLTAGE_DIV_R1_EL,
    VOLTAGE_DIV_R2_EL,
    ADC_REF_VOLTAGE_EL);

VoltageSensor _voltageSensorFC(
    VOLTAGE_SENSOR_PIN_FC,
    VOLTAGE_DIV_R1_FC,
    VOLTAGE_DIV_R2_FC,
    ADC_REF_VOLTAGE_FC);

// Sensor mapping - Maps each analog sensor to its ADS1115 module and channel
const SensorMapping sensorMap[14] = {
    // ADS1 channels (sensors 0-3)
    {&ads1, 0, 0}, // ADC_SENSOR_WATER_LEVEL
    {&ads1, 1, 0}, // ADC_SENSOR_WATER_TEMP
    {&ads1, 2, 0}, // ADC_SENSOR_WATER_PRESS
    {&ads1, 3, 0}, // ADC_SENSOR_ELEC_TEMP

    // ADS2 channels (sensors 4-7)
    {&ads2, 0, 1}, // ADC_SENSOR_H2_TANK_PRESS
    {&ads2, 1, 1}, // ADC_SENSOR_H2_TANK_TEMP
    {&ads2, 2, 1}, // ADC_SENSOR_O2_TANK_PRESS
    {&ads2, 3, 1}, // ADC_SENSOR_O2_TANK_TEMP

    // ADS3 channels (sensors 8-11)
    {&ads3, 0, 2}, // ADC_FC_VOLTAGE
    {&ads3, 1, 2}, // ADC_SENSOR_BMS_VOLTAGE
    {&ads3, 2, 2}, // ADC_SENSOR_ELEC_VOLTAGE
    {&ads3, 3, 2}, // ADC_FC_CURRENT

    // ADS4 channels (sensors 12-13)
    {&ads4, 0, 3}, // ADC_SENSOR_BMS_CURRENT
    {&ads4, 1, 3}  // ADC_SENSOR_ELEC_CURRENT
};

// MovingAverage implementation
MovingAverage::MovingAverage() : sum(0), index(0) {
  memset(readings, 0, sizeof(readings));
}

float MovingAverage::update(float newValue) {
  sum -= readings[index];
  readings[index] = newValue;
  sum += newValue;
  index = (index + 1) % SIZE;
  return sum / SIZE;
}

void MovingAverage::init(float value) {
  for (uint8_t i = 0; i < SIZE; i++) {
    readings[i] = value;
  }
  sum = value * SIZE;
  index = 0;
}

// PredictiveBuffer implementation
PredictiveBuffer::PredictiveBuffer() : index(0), count(0) {
  memset(history, 0, sizeof(history));
}

void PredictiveBuffer::addSample(float sample) {
  history[index++] = sample;
  if (index >= PREDICTIVE_SAMPLES) index = 0;
  if (count < PREDICTIVE_SAMPLES) count++;
}

// Initialize sensors
void initializeSensors() {
	
  _currentSensorEL.calibrate(500);
  _currentSensorFC.calibrate(500);

   // Initialize ADS1115 modules
  ads1.begin(0x48);
  ads2.begin(0x49);
  ads3.begin(0x4A);
  ads4.begin(0x4B);

  // Configure ADS settings
  ads1.setGain(ADS_GAIN);
  ads2.setGain(ADS_GAIN);
  ads3.setGain(ADS_GAIN);
  ads4.setGain(ADS_GAIN);

  ads1.setDataRate(ADS_SPS);
  ads2.setDataRate(ADS_SPS);
  ads3.setDataRate(ADS_SPS);
  ads4.setDataRate(ADS_SPS);

  // Configure alert pins
  pinMode(ADS1_ALERT_PIN, INPUT_PULLUP);
  pinMode(ADS2_ALERT_PIN, INPUT_PULLUP);
  pinMode(ADS3_ALERT_PIN, INPUT_PULLUP);
  pinMode(ADS4_ALERT_PIN, INPUT_PULLUP);

  // Initialize ADS readers
  adsReaders[0] = {&ads1, ADS1_ALERT_PIN, {0, 0, 0, 0}, 0};
  adsReaders[1] = {&ads2, ADS2_ALERT_PIN, {0, 0, 0, 0}, 0};
  adsReaders[2] = {&ads3, ADS3_ALERT_PIN, {0, 0, 0, 0}, 0};
  adsReaders[3] = {&ads4, ADS4_ALERT_PIN, {0, 0, 0, 0}, 0};

  // Start first conversion on each ADS
  for (int i = 0; i < 4; i++) {
    adsReaders[i].ads->startADCReading(0, false);
  }

  // Initialize PZEM modules
  for (int i = 0; i < NUM_PZEMS; i++) {
    pzems[i] = PZEM004Tv30(&PZEM_SERIAL, i + 1);
  }

  // Load calibration data
  loadCalibrationFromEEPROM();

  // Initialize sensor filters
  for (int i = 0; i < CAL_SENSOR_COUNT; i++) {
    sensorFilters[i].init(0.0f);
  }

  // Initialize EL Regulator
  elRegulator.begin();
}

// Update ADS readings
void updateADSReadings() {
  if (!simulationMode) {
    for (int i = 0; i < 4; i++) {
      ADS1115Reader &reader = adsReaders[i];
      if (digitalRead(reader.alertPin) == LOW) {
        int16_t raw = reader.ads->getLastConversionResults();
        reader.readings[reader.currentChannel] = raw;
        reader.currentChannel = (reader.currentChannel + 1) % 4;
        reader.ads->startADCReading(reader.currentChannel, false);
      }
    }
  }
}

// Get filtered and calibrated value from ADS
float getFilteredCalibratedValueADS(uint8_t sensor, uint8_t calIndex) 
{
  if (simulationMode) 
  {
    float simulated = adsSensors[sensor];
    float filtered = sensorFilters[calIndex].update(simulated);
    return (filtered + calibration_data.sensors[calIndex].offset) *
           calibration_data.sensors[calIndex].scale;
  }

  const SensorMapping &mapping = sensorMap[sensor];
  int adsIndex = -1;
  if (mapping.module == &ads1) adsIndex = 0;
  else if (mapping.module == &ads2) adsIndex = 1;
  else if (mapping.module == &ads3) adsIndex = 2;
  else if (mapping.module == &ads4) adsIndex = 3;

  int16_t raw = 0;
  if (adsIndex >= 0) {
    raw = adsReaders[adsIndex].readings[mapping.channel];
  }

  raw = constrain(raw, 0, 32767);
  float voltage = raw * (ADS_FULL_SCALE / 32767.0);
  float filtered = sensorFilters[calIndex].update(voltage);

  return (filtered + calibration_data.sensors[calIndex].offset) *
         calibration_data.sensors[calIndex].scale;
}

// Get filtered and calibrated value from analog pin
float getFilteredCalibratedValue(uint8_t analogPin, uint8_t calIndex) {
  if (simulationMode) {
    uint8_t index = analogPin - A0;
    float simulated = analogSensors[index];
    float filtered = sensorFilters[calIndex].update(simulated);
    return (filtered + calibration_data.sensors[calIndex].offset) *
           calibration_data.sensors[calIndex].scale;
  }

  int raw = analogRead(analogPin);
  raw = constrain(raw, 0, 1023);
  float voltage = raw * (5.0f / 1023.0f); // Convert to 0-5V for Arduino Mega 2560
  float filtered = sensorFilters[calIndex].update(voltage);

  return (filtered + calibration_data.sensors[calIndex].offset) *
         calibration_data.sensors[calIndex].scale;
}

// Calculate slope for predictive analysis
float calculate_slope(const float data[], uint8_t length, float dt) {
  if (length < 2) return 0.0f;

  float sum_x = 0, sum_y = 0, sum_xy = 0, sum_x2 = 0;
  for (uint8_t i = 0; i < length; i++) {
    float x = i * dt;
    float y = data[i];
    sum_x += x;
    sum_y += y;
    sum_xy += x * y;
    sum_x2 += x * x;
  }

  float N = length;
  float denom = (N * sum_x2 - sum_x * sum_x);
  if (fabs(denom) < 1e-6f) return 0.0f;

  return (N * sum_xy - sum_x * sum_y) / denom;
}

// PZEM helper functions
float getPZEMVoltage(uint8_t i) {
  return simulationMode ? pzemModel[i].voltage : pzems[i].voltage();
}

float getPZEMCurrent(uint8_t i) {
  return simulationMode ? pzemModel[i].current : pzems[i].current();
}

float getPZEMPower(uint8_t i) {
  return simulationMode ? pzemModel[i].power : pzems[i].power();
}

float getPZEMEnergy(uint8_t i) {
  return simulationMode ? pzemModel[i].energy : pzems[i].energy();
}

float getPZEMFrequency(uint8_t i) {
  return simulationMode ? pzemModel[i].frequency : pzems[i].frequency();
}

float getPZEMPF(uint8_t i) {
  return simulationMode ? pzemModel[i].pf : pzems[i].pf();
}

float getACSCurrent_EL()
{
    return simulationMode ? currentACS_EL_SimValue : elRegulator.readSupplyCurrent();
}

float getACSCurrent_FC()
{
    return simulationMode ? currentACS_FC_SimValue : _currentSensorFC.readCurrent(100);
}

float getVolt_EL()
{
    return simulationMode ? volt_EL_SimValue : elRegulator.readSupplyVoltage();
}

float getVolt_FC()
{
    return simulationMode ? volt_FC_SimValue : _voltageSensorFC.readVoltage();
}

// Digital I/O wrapper functions
void SetDigitalOutputVal(uint8_t pin, uint8_t value) {
  digitalWrite(pin, value);
  if (simulationMode) {
    // For actuator outputs (pins ACTUATOR_PIN_START to ACTUATOR_PIN_END), use the boolean array.
    if (pin >= ACTUATOR_PIN_START && pin < ACTUATOR_PIN_END) {
      simDigitalOutputs[pin - ACTUATOR_PIN_START] = (value == HIGH);//TURN OFF ACTUATORS
    }
  }
}

uint8_t GetDigitalInputVal(uint8_t pin)
{
  if (simulationMode) {
    if (pin == PIN_ELEC_O2_DRYER_LEVEL)
      return simDigitalInputs.elecO2DryerLevel ? HIGH : LOW;
    if (pin == PIN_ELEC_H2_DRYER_LEVEL)
      return simDigitalInputs.elecH2DryerLevel ? HIGH : LOW;
    if (pin == PIN_FC_O2_DRYER_LEVEL)
      return simDigitalInputs.fcO2DryerLevel ? HIGH : LOW;
    if (pin == PIN_SENSOR_FIRE_DETECT)
      return simDigitalInputs.sensorFireDetect ? HIGH : LOW;
    if (pin == PIN_SENSOR_H2_AMBIENT)
      return simDigitalInputs.sensorH2Ambient ? HIGH : LOW;
    if (pin == PIN_EMERGENCY_STOP)
      return simDigitalInputs.buttonEmergencyStop ? HIGH : LOW;
    if (pin >= ACTUATOR_PIN_START && pin < ACTUATOR_PIN_END)
      return simDigitalOutputs[pin - ACTUATOR_PIN_START] ? HIGH : LOW;
  }
  return digitalRead(pin);
}

// Load calibration from EEPROM
void loadCalibrationFromEEPROM() {
  EEPROM.get(0, calibration_data);

  // Validate calibration data
  if (calibration_data.magic != CALIBRATION_MAGIC_VALUE ||
      calibration_data.version != CALIBRATION_VERSION) {
    // Use default calibration if invalid
    calibration_data = default_calibration;
  }
}

// Save calibration to EEPROM
void saveCalibrationToEEPROM() {
  EEPROM.put(0, calibration_data);
}
