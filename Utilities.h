#ifndef UTILITIES_H
#define UTILITIES_H

/**
 * Utilities.h
 * Provides utility functions used throughout the system
 * Includes logging and CRC calculation
 */

#include <Arduino.h>
#include "Constants.h"

// Forward declaration for TelemetryController
class TelemetryController;
extern TelemetryController telemetry_controller;

/**
 * Logging functions
 * Send messages to serial output and telemetry
 */
void logMessage(LogLevel level, const __FlashStringHelper* msg);
void logMessage(LogLevel level, const __FlashStringHelper* msg, const String &val);
void logMessage(const __FlashStringHelper* message);

/**
 * CRC calculation
 * Computes CRC-CCITT for communication protocol
 * @param data Data buffer to calculate CRC for
 * @param len Length of data buffer
 * @return 16-bit CRC value
 */
uint16_t calculateCRC(const uint8_t* data, uint8_t len);

#endif // UTILITIES_H
