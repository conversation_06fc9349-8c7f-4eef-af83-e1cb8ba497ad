#ifndef VOLTAGE_SENSOR_H
#define VOLTAGE_SENSOR_H
/* VoltageSensor.h */

#include <Arduino.h>

class VoltageSensor {
public:
	/**
	 * @param pin        Arduino analog pin (e.g. A0…A15 on Mega)
	 * @param r1         Top resistor value in ohms (between VIN and pin)
	 * @param r2         Bottom resistor value in ohms (between pin and GND)
	 * @param refVoltage ADC reference voltage in volts (default 5.0)
	 */
	VoltageSensor(uint8_t pin, float r1, float r2, float refVoltage = 5.0);

	/**
	 * Calibrate zero-offset when no voltage is applied (averages multiple samples).
	 * @param samples  Number of readings to average (default 10)
	 */
	void calibrateZero(uint16_t samples = 10);

	/**
	 * @returns the calibrated input voltage in volts
	 */
	float readVoltage() const;

private:
	uint8_t _pin;
	float   _r1, _r2;
	float   _refV;
	float   _offsetV = 0.0;  // stored zero-voltage offset
};

#endif  // VOLTAGE_SENSOR_H